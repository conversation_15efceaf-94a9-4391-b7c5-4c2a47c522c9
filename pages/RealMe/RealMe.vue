<template>
	<view class="page">
		<view class="address-input">
			<view class="list-input s-flex-column" style="margin: 20rpx 0;color: #fe3b0f;">
				<view style="width: 100%;">* 请正确填写本人信息和上传证件</view>
				<view style="width: 100%;">* 实名通过后不可修改且不可重新注册</view>
				<view style="width: 100%;">* 请保持和提现银行卡信息一致</view>
			</view>
		</view>
		<view class="address-input">
			<view class="list-input" v-if="auto">
				<view class="title">
					<text>姓名</text>
				</view>
				<view class="content">
					<input type="text" v-model="postData.username" placeholder="请填姓名">
				</view>
			</view>
			<view class="list-input" v-if="auto">
				<view class="title">
					<text>身份证号</text>
				</view>
				<view class="content">
					<input type="tel" v-model="postData.numbers" placeholder="请填写身份证号">
				</view>
			</view>
			<view class="list-input">
				<view class="title">
					<text>证件图片</text>
				</view>
				<view class="content">
				</view>
			</view>
		</view>
		<view class="address-input" v-if="postData.status==0">
			<view class="list-input s-flex-column" style="margin: 20rpx 0;color: #fe3b0f;">
				<view style="width: 100%;">* 请上传JPG或PNG图片</view>
				<view style="width: 100%;">* 每张图片保持在3M以内</view>
			</view>
		</view>
		<view class="feedback-data">
			<view class="voucher-img" style="margin-bottom: 20rpx;">
				<!-- 				<view class="list" @click="upImg" style="width: 30%;margin-right: 3%;">
					<image src="/static/up_img.png"></image>
				</view> -->
				<view class="list" @click="upImg(1)" style="width: 30vw;margin-right: 3%;width: 43vw;">
					<image :src="postData.img1?postData.img1:api+'/static/zhengICO.jpg'"></image>
				</view>
				<view class="list" @click="upImg(2)" style="width: 30vw;width: 43vw;">
					<image :src="postData.img2?postData.img2:api+'/static/fanICO.jpg'"></image>
				</view>
			</view>
			<button @click="postData.img1='';postData.img2=''" v-if="postData.status==0">清空图片</button>
		</view>

		<view class="feedback-data" v-if="postData.status==0">{{postData.content}}</view>
		<view class="footer-btn">
			<view class="btn" @click="saveAddress">
				<text v-if="postData.status==0">提交审核</text>
				<text v-if="postData.status==1">待审核</text>
				<text v-if="postData.status==2">审核已通过</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				auto:false,
				img1: '',
				img2: '',
				visible: false,
				maskCloseAble: true,
				str: '',
				defaultValue: '510105',
				// defaultValue: ['河北省','唐山市','丰南区'],
				column: 3,
				addressType: '2',
				postData: {
					username: '',
					numbers: '',
					img1: '',
					img2: ''
				},
				tagAction1: 'action',
				tagAction2: '',
				tagAction3: '',
				checked: false,
				id: 0,
				city: '', //记录区县 便于代理统计
				imagePath: '',
				api: getApp().globalData.apiUrl
			};
		},
		onLoad(params) {
			this.$login.checkLogin({
				login: true
			})
			this.addressType = params.type || '2';
			uni.setNavigationBarTitle({
				title: this.addressType === '1' ? '实名认证' : '实名认证'
			})
			this.id = params.id
			this.getAddressInfo()
		},
		methods: {
			upImg($cint) {
				if (this.postData.img1 != '' && this.postData.img2 != '') {
					uni.showToast({
						title: "最多2张图片"
					})
					return false
				}
				uni.chooseImage({
					count: 1, // 只选一张
					sizeType: ['original', 'compressed'], // 可以指定原图或压缩图
					sourceType: ['album', 'camera'], // 可以指定来源是相册或相机
					success: (res) => {
						// 选择图片成功
						this.imagePath = res.tempFilePaths[0];
						this.uploadImage($cint)
					},
					fail: (err) => {
						console.log('选择图片失败', err);
					}
				})
			},
			uploadImage($cint) {
				if (!this.imagePath) {
					uni.showToast({
						title: '请先选择图片',
						icon: 'none'
					});
					return;
				}
				uni.showLoading({
					title: "上传中"
				})
				uni.uploadFile({
					url: this.$http.baseUrl + "upload",
					filePath: this.imagePath,
					name: 'file', // 后端接收文件的字段名
					formData: {
						// 可以带一些额外参数
						token: getApp().globalData.token,
						check:'car',
						imgNum:$cint
					},
					success: (uploadRes) => {
						// 上传成功
						uni.hideLoading()
						var re = JSON.parse(uploadRes.data)
						if(re.code==1){
							uni.showToast({
								title: '上传失败',
								icon: 'none'
							});
							return false
						}
						if ($cint==1) {
							var $checkArr = re.data
							if($checkArr.success==true){
								this.postData.username = $checkArr.name
								this.postData.numbers = $checkArr.num
								this.postData.img1 = re.msg
								this.auto=true
							}else{
								uni.showToast({
									title: '识别失败',
									icon: 'none'
								});
							}
						} else {
							this.postData.img2 = getApp().globalData.apiUrl + re.data.url
						}
					},
					fail: (err) => {
						// console.log('上传失败', err);
						uni.showToast({
							title: '上传失败',
							icon: 'none'
						});
					}
				});
			},
			saveAddress() {
				this.$http.post('realMeSave', {
					token: getApp().globalData.token,
					data: JSON.stringify(this.postData),
					id: this.id
				}).then(res => {
					uni.showToast({
						title: res.msg
					})
				})
			},
			getAddressInfo() {
				this.$http.get('realMeGet', {
					token: getApp().globalData.token,
				}).then(res => {
					if (res.code == 0) {
						this.postData = res.data
						if (this.postData.img1 != '' && this.postData.img2 != '') {
							this.auto=true
						}
					} else {

					}
				})
			},
			selectTag(cint, str) {
				this.clearAction()
				this.postData.tagID = cint
				this.postData.tag = str
				if (cint == 1) {
					this.tagAction1 = 'action'
				}
				if (cint == 2) {
					this.tagAction2 = 'action'
				}
				if (cint == 3) {
					this.tagAction3 = 'action'
				}
			},
			open() {
				this.visible = true
			},
			cancel() {
				this.visible = false
			}
		},
	}
</script>

<style scoped lang="scss">
	@import '../Feedback/Feedback.scss';
	@import 'RealMe.scss';
</style>
