<template>
	<view class="page">
		<!-- 物流信息头部 -->
		<view class="express-header">
		
			<view class="goods-info">
				<view class="goods-thumb">
					<image :src="api + expressInfo.goodsImg" mode="aspectFill"></image>
				</view>
				<view class="goods-title">
					<text class="goods-name">{{expressInfo.goodsTitle || ''}}</text>
					<view class="express-info" v-if="expressInfo.express_info">
						<text class="company-name">{{expressInfo.express_info.logisticsCompanyName + ' ：' + expressInfo.express_info.mailNo}}</text>
						<text class="copy-btn" @click="copyExpressNo(expressInfo.express_info.mailNo)">复制</text>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 物流状态 -->
		<view class="express-status" v-if="expressInfo.express_info">
			<view class="status-info">
				<text class="status-text">{{expressInfo.express_info.logisticsStatusDesc}}</text>
				<text class="status-time">{{expressInfo.express_info.theLastTime}}</text>
			</view>
			<view class="status-message">
				<text>{{expressInfo.express_info.theLastMessage}}</text>
			</view>
		</view>
		
		<!-- 物流轨迹 -->
		<view class="express-timeline">
			<view class="timeline-title">
				<text>物流信息</text>
			</view>
			<view class="timeline-list" v-if="expressInfo.express_info && expressInfo.express_info.logisticsTraceDetailList">
				<view class="timeline-item" v-for="(item, index) in expressInfo.express_info.logisticsTraceDetailList" :key="index" :class="{active: index === 0}">
					<view class="timeline-dot"></view>
					<view class="timeline-content">
						<view class="timeline-time">
							<text>{{item.timeDesc}}</text>
						</view>
						<view class="timeline-desc">
							<text>{{item.desc}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部联系信息 -->
		<view class="express-contact" v-if="expressInfo.express_info.cpMobile">
			<view class="contact-info">
				<text>快递员：{{expressInfo.express_info.cpMobile}}</text>
				<text class="contact-btn" @click="callExpress(expressInfo.express_info.cpMobile)">联系快递员</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				api: getApp().globalData.apiUrl,
				orderID: 0,
				expressInfo: {
					goodsTitle: '',
					goodsImg: '',
					express_info: {
						logisticsCompanyName: '',
						mailNo: '',
						logisticsStatusDesc: '',
						theLastTime: '',
						theLastMessage: '',
						logisticsTraceDetailList: [],
						cpMobile: ''
					}
				}
			};
		},
		onLoad(params) {
			this.$login.checkLogin({login: true})
			this.orderID = params.orderID
			this.getExpressInfo()
		},
		methods: {
			/**
			 * 复制物流单号
			 */
			copyExpressNo(expressNo) {
				uni.setClipboardData({
					data: expressNo,
					success: () => {
						uni.showToast({
							title: '复制成功',
							icon: 'none'
						})
					}
				})
			},

			/**
			 * 获取物流信息
			 */
			getExpressInfo() {
				uni.showLoading({
					title: '加载中...'
				})
				this.$http.get('getExpressInfo', {
					token: getApp().globalData.token,
					orderID: this.orderID,
					myOrder: 1
				}).then(res => {
					uni.hideLoading()
					if (res.code == 0) {
						this.expressInfo = res.data
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none'
						})
						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					}
				}).catch(err => {
					uni.hideLoading()
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					})
					console.error('获取物流信息失败:', err)
				})
			},
			
			/**
			 * 联系快递员
			 * @param {String} mobile 快递员电话
			 */
			callExpress(mobile) {
				if (!mobile) return
				uni.makePhoneCall({
					phoneNumber: mobile
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	@import 'ExpressLog.scss';
</style>