<template>
  <view class="page">
    <!-- 订单状态 -->
    <view class="order-status">
      <view class="status">
        <text class="iconfont icon-zhuyi"></text>
        <text>{{ orderInfo.statusStr }}</text>
      </view>
      <view class="reason">
        <text>当前状态</text>
      </view>
    </view>
    <!-- 收货地址 -->
    <view class="shipping-address" v-if="orderInfo.express==0">
      <view class="name-phone s-flex-bt">
        <view>
          <text class="iconfont icon-dingwei"></text>
          <text>{{ orderInfo.username }}</text>
          <text>{{ orderInfo.mobile }}</text>
        </view>
        <view v-if="orderInfo.statusStr=='待发货'" class="s-btn-s" @click="selectAddress">
          <text class="iconfont icon-edit1" style="color: #fff;"></text>
          修改
        </view>
      </view>
      <view class="address">
        <text>{{ orderInfo.address }}</text>
      </view>
    </view>
    <!-- 订单商品 -->
    <view class="order-goods" style="margin-top: 40rpx;">
      <view class="goods-list">
        <view class="list">
          <view class="thumb">
            <image :src="api + orderInfo.goodsImg" mode=""></image>
          </view>
          <view class="item">
            <view class="title">
              <text class="one-omit">{{ orderInfo.goodsTitle }}</text>
            </view>
            <view class="num-size">
              <text>数量：{{ orderInfo.number }}</text>
              <text></text>
            </view>
            <view class="price">
              <text>￥{{ orderInfo.amount }}</text>
            </view>
            <!-- 						<view class="order-btn" v-if="orderInfo.afterSellStatus<0">
                          <view class="btn" v-if="orderInfo.status>0" @click="cancelAfterSell(orderInfo.id)">
                            <text>取消售后</text>
                          </view>
                          <view class="btn" v-if="orderInfo.afterSellStatus==-2" @click="getPostAddress(orderInfo.id)">
                            <text>退货地址</text>
                          </view>
                        </view>
                        <view class="order-btn" v-else>
                          <view class="btn" v-if="orderInfo.status>0" @click="onApplyAftersales(orderInfo.id)">
                            <text>申请售后</text>
                          </view>
                        </view> -->
          </view>
        </view>
      </view>
      <view class="contact" @click="kefu">
        <text class="iconfont icon-kefu"></text>
        <text>联系客服</text>
      </view>
    </view>
    <!-- 订单信息 -->
    <view class="order-info">
      <view class="info-list">
        <view class="list">
          <view class="title">订单编号:</view>
          <view class="content">
            <text style="font-size: 20rpx;">{{ orderInfo.payShopNumbers }}</text>
            <text class="btn" @click="copyText(orderInfo.payShopNumbers, '订单编号')">复制</text>
          </view>
        </view>
        <view class="list">
          <view class="title">下单时间:</view>
          <view class="content">
            <text>{{ orderInfo.addTime }}</text>
          </view>
        </view>
        <!-- 				<view class="list">
                  <view class="title">支付方式:</view>
                  <view class="content">
                    <text>{{orderInfo.payStr}}</text>
                  </view>
                </view> -->
        <view class="list">
          <view class="title">配送方式:</view>
          <view class="content">
            <text>{{ orderInfo.expressStr }}</text>
          </view>
        </view>
        <block v-if="orderInfo.status>1">
          <view class="list">
            <view class="title">发货日期:</view>
            <view class="content">
              <text>{{ orderInfo.expressTime }}</text>
            </view>
          </view>
          <view class="list" v-for="(item,index) in orderInfo.expressNo" :key="index">
            <view class="title">物流单号{{ index + 1 }}:</view>
            <view class="content">
              <text>{{ item }}</text>
              <text class="btn" @click="copyText(item, '物流单号')">复制</text>
              <text class="btn" @click="viewExpressInfo(orderInfo.id,item)">查看物流</text>
            </view>
          </view>
        </block>
      </view>
    </view>
    <!-- 订单明细 -->
    <view class="order-details">
      <view class="details-list">
        <view class="list">
          <view class="title">
            <text>商品总额</text>
          </view>
          <view class="price">
            <text>￥{{ orderInfo.amount }}</text>
          </view>
        </view>
        <view class="list">
          <view class="title">
            <text>运费</text>
          </view>
          <view class="price">
            <text>+￥0.00</text>
          </view>
        </view>
        <view class="list action">
          <view class="title">
            <text>实付款：</text>
          </view>
          <view class="price">
            <text>￥{{ orderInfo.amount }}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 底部按钮 -->
    <view class="footer-btn" v-if="orderInfo.status==0">
      <view class="del" @click="orderDel(orderInfo.id)">
        <text>删除订单</text>
      </view>
      <view class="btn" @click="gotoPay(orderInfo.id)">
        <!-- <text>查看发票</text> -->
        <text class="action">立即支付</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      ordersID: 0,
      orderInfo: {},
      api: getApp().globalData.apiUrl,
      address: null,
      changeAddress: false
    };
  },
  onLoad(params) {
    this.$login.checkLogin({login: true})
    this.ordersID = params.ordersID
    this.getOrderInfo()
  },
  onShow() {
    this.getAddress()
  },
  methods: {
    /**
     * 复制文本到剪贴板
     * @param {String} text 要复制的文本内容
     * @param {String} type 复制内容的类型，用于提示信息
     */
    copyText(text, type = '内容') {
      // 检查文本是否为空
      if (!text || text.trim() === '') {
        uni.showToast({
          title: `${type}为空，无法复制`,
          icon: 'none',
          duration: 2000
        });
        return;
      }

      // 使用 uni.setClipboardData 复制到剪贴板
      uni.setClipboardData({
        data: text.toString(),
        success: () => {
          uni.showToast({
            title: `${type}已复制`,
            icon: 'success',
            duration: 2000
          });
        },
        fail: (err) => {
          console.error('复制失败:', err);
          uni.showToast({
            title: `${type}复制失败`,
            icon: 'none',
            duration: 2000
          });
        }
      });
    },

    selectAddress() {
      this.changeAddress = true
      uni.navigateTo({
        url: '/pages/AddressList/AddressList',
      })
    },
    getAddress() {
      if (!this.changeAddress) return false;
      var that = this
      var temAddress = getApp().globalData.selectAddress
      if (Object.keys(temAddress).length > 0) {
        that.$http.get('changeAddress', {
          token: getApp().globalData.token,
          orderID: that.ordersID,
          address: JSON.stringify(temAddress)
        }).then(res => {
          uni.showToast({
            title: res.msg
          })
          if (res.code == 0) {
            that.getOrderInfo()
          }
        })

      }

      that.changeAddress = false
    },
    orderDel() {
      var that = this
      uni.showModal({
        title: "是否确定删除?",
        success: function (res) {
          if (res.confirm) {
            that.$http.get('orderDel', {
              token: getApp().globalData.token,
              orderID: that.ordersID
            }).then(res => {
              uni.showToast({
                title: res.msg
              })
              if (res.code == 0) {
                setTimeout(function () {
                  uni.navigateBack()
                }, 1500)
              }
            })
          }
        }
      })
    },
    gotoPay() {
      uni.navigateTo({
        url: "/pages/CashierDesk/CashierDesk?orderID=" + this.ordersID
      })
    },
    kefu() {
      uni.makePhoneCall({
        phoneNumber: getApp().globalData.config.mobile
      })
    },
    gotoKD($str) {
      window.open("https://www.baidu.com/s?wd=" + $str)
    },
    /**
     * 查看物流信息
     * @param {Number} orderId 订单ID
     */
    viewExpressInfo(expressNo) {
      uni.navigateTo({
        url: '/pages/ExpressLog/ExpressLog?orderID=' + orderId + '&expressNo=' + expressNo,
      })
    },
    getOrderInfo() {
      this.$http.get('getOrderInfo', {
        token: getApp().globalData.token,
        orderID: this.ordersID,
        myOrder: 1
      }).then(res => {
        uni.stopPullDownRefresh();
        if (res.code == 0) {
          this.orderInfo = res.data
        } else {
          uni.showToast({
            title: res.msg
          })
        }
      })
    },
    getPostAddress() {
      var that = this
      this.$http.get('getPostAddress', {
        token: getApp().globalData.token,
        orderID: this.ordersID
      }).then(res => {
        if (res.code == 0) {
          uni.showModal({
            title: '请按照以下地址回寄',
            content: res.msg,
            confirmText: '已发快递',
            cancelText: '取消',
            success: (res) => {
              if (res.confirm) {
                uni.showModal({
                  title: '请输入快递单号',
                  editable: true, //是否显示输入框
                  placeholderText: '请输入快递单号', //输入框提示内容
                  confirmText: '确认',
                  cancelText: '取消',
                  success: (res) => {
                    if (res.confirm) {
                      this.$http.get('afterSellPostNumbers', {
                        token: getApp().globalData.token,
                        orderID: this.ordersID,
                        afterSellPostNumbers: res.content,
                      }).then(res => {
                        uni.showToast({
                          title: res.msg
                        })
                        if (res.code == 0) {
                          setTimeout(function () {
                            uni.switchTab({
                              url: "/pages/my/my"
                            })
                          }, 1500)
                        }
                      })
                    }
                  }
                })
              }
            }
          })
        } else {
          uni.showToast({
            title: res.msg
          })
        }
      })
    },
    cancelAfterSell($ordersID) {
      var that = this
      uni.showModal({
        title: "是否确定取消售后",
        success: function (re) {
          if (re.confirm) {
            that.$http.post('cancelAfterSell', {
              token: getApp().globalData.token,
              orderID: that.ordersID,
            }).then(res => {
              uni.showToast({
                title: res.msg
              })
              if (res.code == 0) {
                setTimeout(function () {
                  uni.switchTab({
                    url: "/pages/my/my"
                  })
                }, 1500)
              }
            })
          }
        }
      })
    },
    /**
     * 售后点击
     */
    onApplyAftersales(ordersID) {
      uni.navigateTo({
        url: '/pages/AfterSaleType/AfterSaleType?ordersID=' + ordersID,
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import 'OrderDetails.scss';
</style>
