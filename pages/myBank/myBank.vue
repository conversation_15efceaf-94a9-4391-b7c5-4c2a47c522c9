<template>
	<view class="page">
		<!-- 用户信息列表 -->
		<view class="user-list">

			<view class="list" @click="onNickname(1)">
				<view class="title">
					<text>银行卡号</text>
				</view>
				<view class="more-content">
					<text class="content">{{postData.card_id}}</text>
					<text class="iconfont icon-more more"></text>
				</view>
			</view>
			<view class="list" @click="onNickname(4)">
				<view class="title">
					<text>开户行</text>
				</view>
				<view class="more-content">
					<text class="content">{{postData.card_bank}}</text>
					<text class="iconfont icon-more more"></text>
				</view>
			</view>
			<view class="list">
				<view class="title">
					<text>户名</text>
				</view>
				<view class="more-content">
					<text class="content">{{postData.card_name}}</text>
					<text class="iconfont icon-more more"></text>
				</view>
			</view>
			<view class="list">
				<view class="title">
					<text>身份证号</text>
				</view>
				<view class="more-content">
					<text class="content">{{postData.carID}}</text>
					<text class="iconfont icon-more more"></text>
				</view>
			</view>
<!-- 			<view class="list">
				<view class="title">
					<text>绑定状态</text>
				</view>
				<view class="more-content">
					<text class="content">{{postData.fenzhang}}</text>
					<text class="iconfont icon-more more"></text>
				</view>
			</view> -->
			<view class="btn-info">
			  <view class="btn" style="opacity:1" @click="saveMyBank">
			    <text>保存</text>
			  </view>
			</view>
		</view>
		<!-- 提示框 -->
		<DialogBox ref="DialogBox"></DialogBox>
	</view>
</template>

<script>
	export default {
		data() {
			const currentDate = this.getDate({
					format: true
			})
			return {
				postData:{},
				// 性别
				sexArray: ['男','女','保密'],
				sexIndex: 0,
				sexText: '保密',
				// 生日
				birthdayDate: currentDate,
				startDate: this.getDate('start'),
				endDate: this.getDate('end'),
				birthday: '2020-02-02',
				DialogBox: {},
				// 昵称
				nickname: '',
			};
		},
		onLoad() {
			this.$login.checkLogin({login:true})
			this.getRealNameInfo()
		},
		methods:{
			saveMyBank(){
				uni.showLoading({
					title:"创建中"
				})
				var that = this
				this.$http.post('saveMyBank', {
					token: getApp().globalData.token,
					data:JSON.stringify(that.postData)
				}).then(res => {
					uni.hideLoading()
					uni.showModal({
						content:res.msg
					})
				})
			},
			getMyBank(){
				var that = this
				this.$http.get('getMyBank', {
					token: getApp().globalData.token
				}).then(res => {
					if (res.code == 0) {
						that.postData = res.data
					} else {
						uni.showToast({
							title:res.msg
						})
					}
				})
			},
			/**
			 * 性别
			 * @param {Object} e
			 */
			sexPickerChange(e){
				this.sexIndex = e.detail.value;
				this.sexText = this.sexArray[this.sexIndex];
			},
			/**
			 * 生日
			 * @param {Object} e
			 */
			birthdayPickerChange(e){
				this.birthday = e.detail.value;
			},
			/**
			 * 获取日期
			 * @param {Object} type
			 */
			getDate(type) {
				const date = new Date();
				let year = date.getFullYear();
				let month = date.getMonth() + 1;
				let day = date.getDate();

				if (type === 'start') {
						year = year - 60;
				} else if (type === 'end') {
						year = year + 2;
				}
				month = month > 9 ? month : '0' + month;;
				day = day > 9 ? day : '0' + day;
				return `${year}-${month}-${day}`;
			},
			/**
			 * 昵称点击
			 */
			onNickname($cint){
				var title=""
				var cvalue=""
				if($cint==1){
					title="请输入银行卡号"
					cvalue=this.postData.card_id
				}
				if($cint==2){
					title="请输入户名"
					cvalue=this.postData.card_name
				}
				if($cint==3){
					title="请输入身份证号"
					cvalue=this.postData.cert_id
				}
				if($cint==4){
					title="请输入开户行"
					cvalue=this.postData.card_bank
				}
				this.$refs['DialogBox'].confirm({
					title: '请输入',
					placeholder: '请输入'+title,
					value: cvalue,
					DialogType: 'input',
					animation: 0
				}).then((res)=>{
					var resValue = res.value;
					resValue = resValue.replace(/\s/g, "")
					if($cint==1){
						this.postData.card_id = resValue
					}
					if($cint==2){
						this.postData.card_name = resValue
					}
					if($cint==3){
						this.postData.cert_id = resValue
					}
					if($cint==4){
						this.postData.card_bank = resValue
					}
				})
			},
      getRealNameInfo() {
        this.$http.get('realMeGet', {
          token: getApp().globalData.token,
        }).then(res => {
          if (res.code === 0) {
            if (res.data.status === 2){
              this.getMyBank()
            }else {
              //提示先实名认证，跳转到实名认证页面
              uni.showToast({
                title: "请先实名认证",
                icon: "none",
              })
              setTimeout(() => {
                uni.navigateTo({
                  url: "/pages/RealMe/RealMe"
                })
              }, 2000)
            }

          } else {

          }
        })
      },
		}
	}
</script>

<style scoped lang="scss">
	@import 'myBank.scss';

	/* 按钮 */
	.btn-info{
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  width: 100%;
	  height: 100rpx;
	  margin-top: 40rpx;
	  .btn{
	    display: flex;
	    align-items: center;
	    justify-content: center;
	    width: 90%;
	    height: 80rpx;
	    background: linear-gradient(to right,$base,$change-clor);
	    border-radius: 100rpx;
	    color: #FFFFFF;
	    font-size: 28rpx;
	    opacity: 0.4;
	  }
	}
</style>
