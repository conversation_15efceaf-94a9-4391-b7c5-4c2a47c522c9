.page {
	position: absolute;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #f6f6f6;
}

/* 物流信息头部 */
.express-header {
	width: 100%;
	background-color: #FFFFFF;
	padding: 30rpx 4%;
	border-radius: 0 0 20rpx 20rpx;

	.company-info {
		display: flex;
		flex-direction: column;
		margin-bottom: 20rpx;

		.company-name {
			font-size: 32rpx;
			font-weight: bold;
			color: #222222;
			margin-bottom: 10rpx;
		}

		.express-no {
			font-size: 26rpx;
			color: #959595;
		}
	}

	.goods-info {
		display: flex;
		align-items: center;
		padding: 20rpx 0;
		border-top: 2rpx solid #f6f6f6;

		.goods-thumb {
			width: 120rpx;
			height: 120rpx;
			margin-right: 20rpx;

			image {
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
			}
		}

		.goods-title {
			flex: 1;
			display: flex;
			flex-direction: column;
			gap: 16rpx;

			.goods-name {
				font-size: 28rpx;
				color: #222222;
				font-weight: bold;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}

			.express-info {
				display: flex;
				align-items: center;
				gap: 20rpx;

				.company-name {
					font-size: 26rpx;
					color: #666666;
					flex: 1;
				}

				.copy-btn {
					font-size: 24rpx;
					color: #ffffff;
					background-color: #f23030;
					padding: 6rpx 20rpx;
					border-radius: 30rpx;
				}
			}
		}
	}
}

/* 物流状态 */
.express-status {
	width: 100%;
	background-color: #FFFFFF;
	padding: 30rpx 4%;
	margin-top: 20rpx;
	border-radius: 20rpx;

	.status-info {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;

		.status-text {
			font-size: 32rpx;
			font-weight: bold;
			color: #f23030;
		}

		.status-time {
			font-size: 26rpx;
			color: #959595;
		}
	}

	.status-message {
		text {
			font-size: 28rpx;
			color: #555555;
			line-height: 1.5;
		}
	}
}

/* 物流轨迹 */
.express-timeline {
	width: 100%;
	background-color: #FFFFFF;
	padding: 30rpx 4%;
	margin-top: 20rpx;
	border-radius: 20rpx;
	padding-bottom: 100rpx;

	.timeline-title {
		margin-bottom: 30rpx;

		text {
			font-size: 32rpx;
			font-weight: bold;
			color: #222222;
		}
	}

	.timeline-list {
		position: relative;

		&::before {
			content: '';
			position: absolute;
			left: 10rpx;
			top: 0;
			width: 2rpx;
			height: 100%;
			background-color: #EEEEEE;
		}

		.timeline-item {
			position: relative;
			padding-left: 40rpx;
			margin-bottom: 40rpx;

			.timeline-dot {
				position: absolute;
				left: 0;
				top: 10rpx;
				width: 20rpx;
				height: 20rpx;
				border-radius: 50%;
				background-color: #CCCCCC;
				z-index: 1;
			}

			.timeline-content {
				.timeline-time {
					margin-bottom: 10rpx;

					text {
						font-size: 26rpx;
						color: #959595;
					}
				}

				.timeline-desc {
					text {
						font-size: 28rpx;
						color: #555555;
						line-height: 1.5;
					}
				}
			}

			&.active {
				.timeline-dot {
					background-color: #f23030;
					width: 24rpx;
					height: 24rpx;
				}

				.timeline-content {
					.timeline-time {
						text {
							color: #f23030;
							font-weight: bold;
						}
					}

					.timeline-desc {
						text {
							color: #222222;
							font-weight: bold;
						}
					}
				}
			}
		}
	}
}

/* 底部联系信息 */
.express-contact {
	width: 100%;
	background-color: #FFFFFF;
	padding: 30rpx 4%;
	margin-top: 20rpx;
	border-radius: 20rpx;

	.contact-info {
		display: flex;
		justify-content: space-between;
		align-items: center;

		text {
			font-size: 28rpx;
			color: #555555;
		}

		.contact-btn {
			padding: 10rpx 30rpx;
			background-color: #f23030;
			color: #FFFFFF;
			border-radius: 100rpx;
			font-size: 26rpx;
		}
	}
}
